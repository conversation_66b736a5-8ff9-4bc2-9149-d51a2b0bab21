from typing import Optional
from sqlalchemy.orm import joinedload
from utils.sqlalchemy_db import DatabaseSession
from models.sqlalchemy_models import VehicleModelORM, ComponentORM, TestProjectORM, ModalDataORM
from models.vehicle import ModalData  # 保留原始 dataclass 用于接口兼容
from utils.response import Result
import logging


class ModalService:
    """模态数据服务 - 使用 SQLAlchemy ORM 优化"""

    def get_vehicles(self) -> Result:
        """获取所有车型"""
        try:
            with DatabaseSession() as session:
                vehicles = session.query(VehicleModelORM).filter(
                    VehicleModelORM.status == 'active'
                ).order_by(VehicleModelORM.vehicle_model_name).all()

                vehicle_list = [vehicle.to_dict() for vehicle in vehicles]
                return Result.success(vehicle_list, "获取车型列表成功")
        except Exception as e:
            logging.error(f"获取车型列表失败: {e}")
            return Result.error("获取车型列表失败")

    def get_components(self) -> Result:
        """获取所有零部件"""
        try:
            with DatabaseSession() as session:
                components = session.query(ComponentORM).order_by(
                    ComponentORM.category,
                    ComponentORM.sub_category,
                    ComponentORM.component_name
                ).all()

                component_list = [component.to_dict() for component in components]
                return Result.success(component_list, "获取零部件列表成功")
        except Exception as e:
            logging.error(f"获取零部件列表失败: {e}")
            return Result.error("获取零部件列表失败")

    def search_by_vehicle(self, vehicle_code: str, component_code: Optional[str] = None) -> Result:
        """按车型搜索模态数据"""
        try:
            with DatabaseSession() as session:
                # 构建查询
                query = session.query(ModalDataORM).join(
                    TestProjectORM, ModalDataORM.test_project_id == TestProjectORM.id
                ).join(
                    VehicleModelORM, TestProjectORM.vehicle_model_id == VehicleModelORM.id
                ).outerjoin(
                    ComponentORM, TestProjectORM.component_id == ComponentORM.id
                ).filter(
                    VehicleModelORM.vehicle_model_code == vehicle_code
                )

                # 如果指定了零部件，添加条件
                if component_code and component_code != 'all':
                    query = query.filter(ComponentORM.component_code == component_code)

                # 预加载关联数据
                query = query.options(
                    joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.vehicle_model),
                    joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.component)
                )

                # 排序
                query = query.order_by(
                    ComponentORM.category,
                    ComponentORM.sub_category,
                    ModalDataORM.frequency
                )

                modal_data_list = query.all()

                # 构建返回数据
                result = []
                for md in modal_data_list:
                    data = {
                        'id': md.id,
                        'mode_order': md.mode_order,
                        'direction': md.direction,
                        'frequency': md.frequency,
                        'damping_ratio': md.damping_ratio,
                        'mode_shape_description': md.mode_shape_description,
                        'mode_shape_file': md.mode_shape_file,
                        'test_photo_file': md.test_photo_file,
                        'vehicle_model_code': md.test_project.vehicle_model.vehicle_model_code,
                        'vehicle_model_name': md.test_project.vehicle_model.vehicle_model_name,
                        'component_code': md.test_project.component.component_code if md.test_project.component else None,
                        'component_name': md.test_project.component.component_name if md.test_project.component else None,
                        'category': md.test_project.component.category if md.test_project.component else None,
                        'sub_category': md.test_project.component.sub_category if md.test_project.component else None,
                        'test_status': md.test_project.test_status,
                        'test_date': md.test_project.test_date.isoformat() if md.test_project.test_date else None,
                        'test_engineer': md.test_project.test_engineer
                    }
                    result.append(data)

                return Result.success(result, f"搜索到 {len(result)} 条模态数据")
        except Exception as e:
            logging.error(f"按车型搜索模态数据失败: {e}")
            return Result.error("按车型搜索模态数据失败")

    def search_by_component(self, vehicle_code: str, status: Optional[str] = None,
                            order: Optional[str] = None) -> Result:
        """按零件搜索模态数据"""
        try:
            with DatabaseSession() as session:
                # 构建查询
                query = session.query(ModalDataORM).join(
                    TestProjectORM, ModalDataORM.test_project_id == TestProjectORM.id
                ).join(
                    VehicleModelORM, TestProjectORM.vehicle_model_id == VehicleModelORM.id
                ).outerjoin(
                    ComponentORM, TestProjectORM.component_id == ComponentORM.id
                ).filter(
                    VehicleModelORM.vehicle_model_code == vehicle_code
                )

                # 添加状态条件
                if status:
                    query = query.filter(TestProjectORM.test_status == status)

                # 添加阶次条件
                if order and order != 'all':
                    query = query.filter(ModalDataORM.mode_order == int(order))

                # 预加载关联数据
                query = query.options(
                    joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.vehicle_model),
                    joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.component)
                )

                # 排序
                query = query.order_by(
                    VehicleModelORM.vehicle_model_name,
                    ComponentORM.category,
                    ModalDataORM.frequency
                )

                modal_data_list = query.all()

                # 构建返回数据
                result = []
                for md in modal_data_list:
                    data = {
                        'id': md.id,
                        'mode_order': md.mode_order,
                        'direction': md.direction,
                        'frequency': md.frequency,
                        'damping_ratio': md.damping_ratio,
                        'mode_shape_description': md.mode_shape_description,
                        'mode_shape_file': md.mode_shape_file,
                        'test_photo_file': md.test_photo_file,
                        'vehicle_model_code': md.test_project.vehicle_model.vehicle_model_code,
                        'vehicle_model_name': md.test_project.vehicle_model.vehicle_model_name,
                        'component_code': md.test_project.component.component_code if md.test_project.component else None,
                        'component_name': md.test_project.component.component_name if md.test_project.component else None,
                        'category': md.test_project.component.category if md.test_project.component else None,
                        'sub_category': md.test_project.component.sub_category if md.test_project.component else None,
                        'test_status': md.test_project.test_status,
                        'test_date': md.test_project.test_date.isoformat() if md.test_project.test_date else None,
                        'test_engineer': md.test_project.test_engineer
                    }
                    result.append(data)

                return Result.success(result, f"搜索到 {len(result)} 条模态数据")
        except Exception as e:
            logging.error(f"按零件搜索模态数据失败: {e}")
            return Result.error("按零件搜索模态数据失败")

    def get_modal_detail(self, modal_id: int) -> Result:
        """获取模态数据详情"""
        try:
            with DatabaseSession() as session:
                modal_data = session.query(ModalDataORM).options(
                    joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.vehicle_model),
                    joinedload(ModalDataORM.test_project).joinedload(TestProjectORM.component)
                ).filter(ModalDataORM.id == modal_id).first()

                if not modal_data:
                    return Result.not_found("模态数据不存在")

                data = {
                    'id': modal_data.id,
                    'mode_order': modal_data.mode_order,
                    'direction': modal_data.direction,
                    'frequency': modal_data.frequency,
                    'damping_ratio': modal_data.damping_ratio,
                    'mode_shape_description': modal_data.mode_shape_description,
                    'mode_shape_file': modal_data.mode_shape_file,
                    'test_photo_file': modal_data.test_photo_file,
                    'notes': modal_data.notes,
                    'vehicle_model_code': modal_data.test_project.vehicle_model.vehicle_model_code,
                    'vehicle_model_name': modal_data.test_project.vehicle_model.vehicle_model_name,
                    'component_code': modal_data.test_project.component.component_code if modal_data.test_project.component else None,
                    'component_name': modal_data.test_project.component.component_name if modal_data.test_project.component else None,
                    'category': modal_data.test_project.component.category if modal_data.test_project.component else None,
                    'sub_category': modal_data.test_project.component.sub_category if modal_data.test_project.component else None,
                    'test_status': modal_data.test_project.test_status,
                    'test_date': modal_data.test_project.test_date.isoformat() if modal_data.test_project.test_date else None,
                    'test_engineer': modal_data.test_project.test_engineer,
                    'test_condition': modal_data.test_project.test_condition,
                    'excitation_method': modal_data.test_project.excitation_method
                }

                return Result.success(data, "获取模态数据详情成功")
        except Exception as e:
            logging.error(f"获取模态数据详情失败: {e}")
            return Result.error("获取模态数据详情失败")

    def create_modal_data(self, modal_data: ModalData) -> Result:
        """创建模态数据"""
        try:
            with DatabaseSession() as session:
                # 创建 SQLAlchemy ORM 对象
                new_modal_data = ModalDataORM(
                    test_project_id=modal_data.test_project_id,
                    mode_order=modal_data.mode_order,
                    direction=modal_data.direction,
                    frequency=modal_data.frequency,
                    damping_ratio=modal_data.damping_ratio,
                    mode_shape_description=modal_data.mode_shape_description,
                    mode_shape_file=modal_data.mode_shape_file,
                    test_photo_file=modal_data.test_photo_file,
                    notes=modal_data.notes,
                    updated_by=modal_data.updated_by
                )

                session.add(new_modal_data)
                session.flush()  # 获取 ID
                return Result.success({'id': new_modal_data.id}, "创建模态数据成功")
        except Exception as e:
            logging.error(f"创建模态数据失败: {e}")
            return Result.error("创建模态数据失败")

    def update_modal_data(self, modal_id: int, modal_data: ModalData) -> Result:
        """更新模态数据"""
        try:
            with DatabaseSession() as session:
                # 查找要更新的记录
                existing_modal_data = session.query(ModalDataORM).filter(
                    ModalDataORM.id == modal_id
                ).first()

                if not existing_modal_data:
                    return Result.not_found("模态数据不存在")

                # 更新字段
                existing_modal_data.mode_order = modal_data.mode_order
                existing_modal_data.direction = modal_data.direction
                existing_modal_data.frequency = modal_data.frequency
                existing_modal_data.damping_ratio = modal_data.damping_ratio
                existing_modal_data.mode_shape_description = modal_data.mode_shape_description
                existing_modal_data.mode_shape_file = modal_data.mode_shape_file
                existing_modal_data.test_photo_file = modal_data.test_photo_file
                existing_modal_data.notes = modal_data.notes
                existing_modal_data.updated_by = modal_data.updated_by

                return Result.success(None, "更新模态数据成功")
        except Exception as e:
            logging.error(f"更新模态数据失败: {e}")
            return Result.error("更新模态数据失败")

    def delete_modal_data(self, modal_id: int) -> Result:
        """删除模态数据"""
        try:
            with DatabaseSession() as session:
                # 查找要删除的记录
                modal_data = session.query(ModalDataORM).filter(
                    ModalDataORM.id == modal_id
                ).first()

                if not modal_data:
                    return Result.not_found("模态数据不存在")

                session.delete(modal_data)
                return Result.success(None, "删除模态数据成功")
        except Exception as e:
            logging.error(f"删除模态数据失败: {e}")
            return Result.error("删除模态数据失败")


# 创建服务实例
modal_service = ModalService()
