from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session
from config import Config
import logging

# 创建数据库引擎
def create_db_engine():
    """创建数据库引擎"""
    try:
        database_url = Config.get_database_url()

        engine = create_engine(
            database_url,
            **Config.SQLALCHEMY_ENGINE_OPTIONS
        )

        logging.info("SQLAlchemy 数据库引擎创建成功")
        return engine
    except Exception as e:
        logging.error(f"创建数据库引擎失败: {e}")
        raise e

# 创建全局引擎
engine = create_db_engine()

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建线程安全的会话
Session = scoped_session(SessionLocal)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()

def get_db_session():
    """获取数据库会话"""
    return Session()

def close_db_session():
    """关闭数据库会话"""
    Session.remove()

class DatabaseSession:
    """数据库会话上下文管理器"""
    
    def __init__(self):
        self.session = None
    
    def __enter__(self):
        self.session = get_db_session()
        return self.session
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.session.rollback()
        else:
            self.session.commit()
        self.session.close()
